import React, { useState, useEffect } from "react";
import {reqFormGATrackREC,currentISO,isset, getparamValREC, readCookieREC} from '../common/formCommfun';
// import { useGlobalState } from "../context/store";
import AdvSearchUnit from "./AdvSearchUnit";
import { callAdvSearchAPI } from "../common/AdvSearchAPI";
import callMiniDetAPI from "../MinidtlsAPI";
function ThankyouregOld({ form_param }) {
    // const { state } = useGlobalState();
    // const [plaData, setPlaData] = useState([]);
    const [searchAPIdata, setSearchAPIdata] = useState([]);
    const [prodNm, setProdNm] = useState();

    useEffect(() => {
        const srchapicall = async () =>  {
            let imeshcookie = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
            const glid = getparamValREC(imeshcookie, 'glid');
            let cityminidet = '';
            let pageCityId = getparamValREC(imeshcookie, 'ctid') ? getparamValREC(imeshcookie, 'ctid') : '';
            if(!pageCityId){
                try {
                    const storedECV = sessionStorage.getItem(`ecv-${glid}`);
                    if (storedECV) {
                        cityminidet = storedECV;
                    }
                } catch (error) {
                    console.error('Error accessing session storage:', error);
                }
                if (!cityminidet) {
                    const data = await callMiniDetAPI(form_param);
                    cityminidet = data && data.Response && data.Response["ECV"] ? data.Response["ECV"] : "";
                }
            }
            if(cityminidet || pageCityId){
                advSearchdata(form_param, cityminidet, pageCityId);
            }
        }
        srchapicall();
    }, [form_param]);


    async function advSearchdata(form_param , cityminidet, pageCityId) {
        if(form_param.mcatName || form_param.prodDispName || form_param.prodName){
            let prod=form_param.mcatName || form_param.prodDispName || form_param.prodName;
            setProdNm(prod);
            const results = await callAdvSearchAPI(form_param,'ty',pageCityId,cityminidet);
            setSearchAPIdata(results);
        }
    }
    if(isset(()=>searchAPIdata) && searchAPIdata.length>0){
        reqFormGATrackREC("ThankYouShown_AdvSearch", form_param);
    }

    return (
        <>
        {isset(()=>searchAPIdata) && searchAPIdata.length>0  ?  <AdvSearchUnit form_param={form_param} searchAPIdata={searchAPIdata} prodNm={prodNm} />:(<div></div>) }
        </>
    );
}

export default ThankyouregOld;