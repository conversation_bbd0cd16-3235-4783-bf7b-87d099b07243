import React, { useState, useEffect } from "react";
import {reqFormGATrackREC,currentISO,isset, getparamValREC, readCookieREC} from '../common/formCommfun';
import { useGlobalState } from "../context/store";
import AdvSearchUnit from "./AdvSearchUnit";
import { callAdvSearchAPI } from "../common/AdvSearchAPI";
import callMiniDetAPI from "../MinidtlsAPI";
function ThankyouregOld({ form_param }) {
    const { state } = useGlobalState();
    // const [plaData, setPlaData] = useState([]);
    const [searchAPIdata, setSearchAPIdata] = useState([]);
    const [prodNm, setProdNm] = useState();

    useEffect(() => {
        const srchapicall = async () =>  {
            let imeshcookie = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
            const glid = getparamValREC(imeshcookie, 'glid');
            let cityminidet = '';
            let pageCityId = getparamValREC(imeshcookie, 'ctid') ? getparamValREC(imeshcookie, 'ctid') : '';
            if(!pageCityId){
                try {
                    const storedECV = sessionStorage.getItem(`ecv-${glid}`);
                    if (storedECV) {
                        cityminidet = storedECV;
                    }
                } catch (error) {
                    console.error('Error accessing session storage:', error);
                }
                if (!cityminidet) {
                    const data = await callMiniDetAPI(form_param);
                    cityminidet = data && data.Response && data.Response["ECV"] ? data.Response["ECV"] : "";
                }
            }
            if(cityminidet || pageCityId){
                advSearchdata(form_param, cityminidet, pageCityId);
            }
        }
        srchapicall();
    }, [form_param]);

    // const retrieveData = (id) => {
    //     try {
    //       return JSON.parse(sessionStorage.getItem("plaWidget-" + id )) || null;
    //     } catch (err) {
    //       console.error("Failed to retrieve sessionStorage item:", err);
    //       return null;
    //     }
    //   };
    
      const srchQuryHandling = (route) => {
        route=route.replace(/%20/g, '+')
        return route;
    }


    async function advSearchdata(form_param , cityminidet, pageCityId) {
        if(form_param.mcatName || form_param.prodDispName || form_param.prodName){
            let prod=form_param.mcatName || form_param.prodDispName || form_param.prodName;
            setProdNm(prod);
            if(state.srchapiresult){
                setSearchAPIdata(state.srchapiresult);
                return;
            }
            const results = await callAdvSearchAPI(form_param,'ty',pageCityId,cityminidet);
            setSearchAPIdata(results);
        }
    }

    // async function plawidget(form_param) {

    //     const widgetData = retrieveData(form_param.mcatId);
    //     if(!widgetData && isset(()=>form_param.mcatId) && form_param.mcatId!=-1){
    //         try {
    //             const parmObj = {
    //                 modid: form_param.modId,
    //                 mcatid: form_param.mcatId
    //             };
    
    //             const queryParams = new URLSearchParams(parmObj);
    //             const webAddressLocation = location.host;
    //             const ServerName = webAddressLocation.match(/^(dev|localhost)/) ? "dev-" : (webAddressLocation.match(/^stg/) ? "stg-" : "");
    //             const response = await fetch(`https://apps.imimg.com/index.php?r=Newreqform/WidgetDataNew&${queryParams}`);
    
    //             if (!response.ok) {
    //                 throw new Error('Network response was not ok');
    //             }
    
    //             const data = await response.json();
    //             if (data && data.STATUS === 200 && data["RECOMMMENDATON_CATEGORY_BASED"] && data["RECOMMMENDATON_CATEGORY_BASED"].length > 0) {
    //                 sessionStorage.setItem("plaWidget-" + form_param.mcatId, JSON.stringify(data["RECOMMMENDATON_CATEGORY_BASED"]));
    //                 setPlaData(data["RECOMMMENDATON_CATEGORY_BASED"]);
    //             }
    //         } catch (error) {
    //             console.error('There was a problem with the fetch operation:', error);
    //             imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'Widget_data','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
    //         }

    //     }
    //     else {
    //         setPlaData(widgetData);
    //       }
        
    // }

    // const capsdata = window.CapsData || [];

    // Determine how many items to take from plaData and capsdata
    // const plaDataToShow = plaData.slice(0, 5);
    // const capsDataToShow = capsdata.slice(0, Math.max(0, 5 - plaDataToShow.length));
    // let combinedData=[];
    // if(currentISO()=='IN'){
    //     combinedData = plaData.concat(capsdata);
    // }
    // else{
    //     combinedData = capsdata;
    // }
    // combinedData= combinedData.slice(0, 5);
    // if(plaData.length>= 5 && currentISO()=='IN'){
    //     reqFormGATrackREC("ThankYouShown_Ecom",form_param);
    // }else if(plaData.length= 0 && capsdata.length >=5){
    //     reqFormGATrackREC("ThankYouShown_Caps",form_param);
    // }else{
    //     reqFormGATrackREC("ThankYouShown_EcomCaps",form_param)
    // }
    if(isset(()=>searchAPIdata) && searchAPIdata.length>0){
        reqFormGATrackREC("ThankYouShown_AdvSearch", form_param);
    }

    return (
        <>
        {isset(()=>searchAPIdata) && searchAPIdata.length>0  ?  <AdvSearchUnit form_param={form_param} searchAPIdata={searchAPIdata} prodNm={prodNm} />:(<div></div>) }
        </>
    );
}

export default ThankyouregOld;