import React, { useEffect } from 'react';
import { StateProvider } from '../context/store';
import WhichForm from './WhichForm';
import { defState } from '../common/defaultState';
import { ErrorsLOG } from '../common/formCommfun';
import ErrorBoundary from '../ErrorBoundary';

function OpenForm({ form_param, type, closeFn , fromLoad}) {
    // defState.form_param = form_param;
    useEffect(() => {
        const scriptId = 'google-api-script';

        // Check if the script is already added
        if (!document.getElementById(scriptId)) {
            const script = document.createElement('script');
            script.id = scriptId;
            script.src = "https://apis.google.com/js/platform.js";
            script.async = true;
            document.head.appendChild(script);
           
            // Cleanup script when component unmounts
            return () => {
                document.head.removeChild(script);
            };
        }

        window.onerror = ErrorsLOG; 
       
    }, []);

    return (
        //  <ErrorBoundary>
            <StateProvider serviceData={defState}>
            {fromLoad && <WhichForm form_param={form_param} type={type} closeFn={closeFn} />}
        </StateProvider>
        // </ErrorBoundary>
        
    );
}

export default OpenForm;