/* Local Seller Card Styles */
.local-seller-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
  max-width: 470px;
  width: 100%;
}

/* Header Section */
.local-seller-header {
  background: linear-gradient(135deg, #00a699 0%, #00877c 100%);
  color: white;
  padding: 12px 16px;
  display: flex;
  gap: 4px;
}

.header-text {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.location-text {
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 1px;
}

/* Product Card Section */
.product-card-section {
  padding: 16px;
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.product-image-container {
  flex-shrink: 0;
  width: 100px;
  height: 100px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  text-align: center;
}

.product-image {
  max-height: 100%;
  max-width: 100%;
}

.product-details-container {
  flex: 1;
  min-width: 0;
}

/* Product Rating */
.product-rating {
  margin-bottom: 12px;
}

/* Two Column Layout */
.product-info-columns {
  display: flex;
  gap: 5px;
  align-items: flex-start;
}

.left-column {
  flex: 1;
}

.right-column {
  flex: 1;
}
.product-title-seller {
  margin-bottom: 5px;
}

.product-title-seller a {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  text-decoration: none;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-title-seller a:hover {
  color: #00a699;
}

/* Member and Response Info */
.member-response-info {
  display: flex;
  flex-direction: row;
  gap: 8px;
  font-size: 13px;
  align-items: center;
}



/* Price Styles */
.product-price-seller {
  font-size: 16px;
  font-weight: bold;
  color: #00a699;
  margin-bottom: 8px;
}

/* Seller Info */
.seller-info {
  margin-bottom: 8px;
  font-size: 10px;
}

.sold-by-text {
  color: #666;
}

.seller-name {
  color: #333;
  text-decoration: none;
  font-weight: 500;
}

.seller-name:hover {
  color: #00a699;
  text-decoration: underline;
}

/* GST and Member Info - Side by Side */
.gst-member-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
}

/* Supplier Type Info */
.supplier-type-info {
  margin-bottom: 8px;
  font-size: 13px;
}

/* Response Rate Info */
.response-rate-info {
  font-size: 13px;
}

/* Action Section */
.action-section {
  padding: 0 16px 16px;
}

.highlight-box {
  border: 2px solid #ffb366;
  border-radius: 8px;
  background: #fff8f0;
  padding: 12px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: space-between;
}

.no-btn,
.yes-btn {
  flex: 1;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid;
  background: white;
}

.no-btn {
  color: #dc3545;
  border-color: #ffcdd2;
}

.no-btn:hover {
  background: #ffebee;
  border-color: #ef9a9a;
  color: #c62828;
}

.yes-btn {
  color: #4caf50;
  border-color: #c8e6c9;
}

.yes-btn:hover {
  background: #e8f5e8;
  border-color: #81c784;
  color: #2e7d32;
}

/* Responsive Design */
@media (max-width: 480px) {
  .local-seller-card {
    max-width: 100%;
  }

  .product-card-section {
    padding: 12px;
  }

  .product-image {
    width: 70px;
    height: 70px;
  }

  .product-info-columns {
    flex-direction: column;
    gap: 12px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .no-btn,
  .yes-btn {
    width: 100%;
  }
}
