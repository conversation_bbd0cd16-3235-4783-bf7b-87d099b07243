/* Local Seller Card Styles */
.local-seller-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
  max-width: 470px;
  width: 100%;
}

/* Header Section */
.local-seller-header {
  background: linear-gradient(135deg, #00a699 0%, #00877c 100%);
  color: white;
  padding: 12px 16px;
  text-align: center;
}

.header-text {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.location-text {
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 1px;
}

/* Product Card Section */
.product-card-section {
  padding: 16px;
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.product-image-container {
  flex-shrink: 0;
}

.product-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.product-details-container {
  flex: 1;
  min-width: 0;
}

/* Product Rating */
.product-rating {
  margin-bottom: 12px;
}

/* Two Column Layout */
.product-info-columns {
  display: flex;
  gap: 5px;
  align-items: flex-start;
}

.left-column {
  flex: 1;
}

.right-column {
  flex: 1;
}
.product-title-seller a {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  text-decoration: none;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-title-seller a:hover {
  color: #00a699;
}

/* Member and Response Info */
.member-response-info {
  display: flex;
  flex-direction: row;
  gap: 8px;
  font-size: 13px;
  align-items: center;
}



/* Price Styles */
.product-price-seller {
  font-size: 16px;
  font-weight: bold;
  color: #00a699;
  margin-bottom: 8px;
}

/* Seller Info */
.seller-info {
  margin-bottom: 8px;
  font-size: 10px;
}

.sold-by-text {
  color: #666;
}

.seller-name {
  color: #333;
  text-decoration: none;
  font-weight: 500;
}

.seller-name:hover {
  color: #00a699;
  text-decoration: underline;
}

/* Trust Indicators */
.trust-indicators {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
}

/* Action Section */
.action-section {
  padding: 0 16px 16px;
}

.highlight-box {
  border: 2px solid #ffb366;
  border-radius: 8px;
  background: #fff8f0;
  padding: 12px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: space-between;
}

.no-thanks-btn,
.yes-deals-btn {
  flex: 1;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid;
}

.no-thanks-btn {
  background: white;
  color: #666;
  border-color: #ddd;
}

.no-thanks-btn:hover {
  background: #f5f5f5;
  border-color: #999;
  color: #333;
}

.yes-deals-btn {
  background: linear-gradient(135deg, #00a699 0%, #00877c 100%);
  color: white;
  border-color: #00a699;
}

.yes-deals-btn:hover {
  background: linear-gradient(135deg, #00877c 0%, #006b5c 100%);
  border-color: #00877c;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 480px) {
  .local-seller-card {
    max-width: 100%;
  }

  .product-card-section {
    padding: 12px;
  }

  .product-image {
    width: 70px;
    height: 70px;
  }

  .product-info-columns {
    flex-direction: column;
    gap: 12px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .no-thanks-btn,
  .yes-deals-btn {
    width: 100%;
  }
}
