/* Local Seller Card Styles */
.local-seller-card {
  background: #f0fdf4;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 16px 0px;
  overflow: hidden;
  max-width: 520px;
  width: 100%;
  border: 1px solid #d3e7dc;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.local-seller-card.unmounting {
  opacity: 0;
  transform: translateY(-10px);
}

/* Text Section */
.text {
  color: #323232;
  padding: 10px 16px 0px;
  font-size: 15px;
  font-weight: 500;
  line-height: 1.4;
}

/* City styling */
.city-name {
  color: #00a699;
  font-weight: 600;
}

/* Seller name link styling */
/* .seller-name {
  color: #00a699;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.seller-name:hover {
  color: #007a6e;
  text-decoration: underline;
} */

/* Product title link styling */
.text a {
  color: #00a699;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.text a:hover {
  color: #007a6e;
  text-decoration: underline;
}

/* Confirmation Message */
.confirmation-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 16px;
  gap: 12px;
}

.tick-mark {
  background: #4caf50;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  animation: tickAnimation 0.5s ease-in-out;
}

.confirmation-text {
  color: #323232;
  font-size: 15px;
  font-weight: 500;
}

@keyframes tickAnimation {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Action Section */
.action-section {
  padding: 0 16px 16px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: end;
}

.no-btn,
.yes-btn {
  width: 110px;
  padding: 7px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid;
  background: white;
}

.no-btn {
  color: #dd6d78;
  border-color: #ffcdd2;
}

.no-btn:hover {
  background: #ffebee;
  border-color: #ef9a9a;
  color: #c62828;
}

.yes-btn {
  color: #4caf50;
  border-color: #c8e6c9;
}

.yes-btn:hover {
  background: #e8f5e8;
  border-color: #81c784;
  color: #2e7d32;
}

/* Responsive Design */
@media (max-width: 480px) {
  .local-seller-card {
    max-width: 100%;
  }

  .text {
    padding: 12px;
    font-size: 14px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }
}
