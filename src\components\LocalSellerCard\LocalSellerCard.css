/* Local Seller Card Styles */
.local-seller-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 16px 0px;
  overflow: hidden;
  max-width: 520px;
  width: 100%;
  border: 1px solid #d3e7dc;
  transition: opacity 0.3s ease, transform 0.3s ease;
  min-height: 105px;
  display: flex;
  flex-direction: column;
}

.local-seller-card.unmounting {
  opacity: 0;
  transform: translateY(-10px);
}

/* Text Section */
.text {
  color: #323232;
  padding: 10px 16px 0px;
  font-size: 15px;
  font-weight: 500;
  line-height: 1.4;
}

/* City styling */
.city-name {
  color: #029085; /* Teal green */
  font-weight: 600;
}

/* Company/Seller name link styling */
.seller-name {
  color: #0066cc; /* Professional blue */
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.seller-name:hover {
  color: #0052a3; /* Darker blue on hover */
  text-decoration: underline;
}

/* Product title link styling */
.text a:not(.seller-name) {
  color: #1f437b; /* Darker blue */
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.text a:not(.seller-name):hover {
  color: #1e3a8a; /* Even darker blue on hover */
  text-decoration: underline;
}

/* Confirmation Message */
.confirmation-message {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: 12px;
  min-height: 120px;
}

.tick-mark {
  background: #4caf50;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  animation: tickAnimation 0.5s ease-in-out;
}

.confirmation-text {
  color: #323232;
  font-size: 15px;
  font-weight: 500;
}

@keyframes tickAnimation {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Action Section */
.action-section {
  padding: 0 16px 16px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: end;
}

.no-btn,
.yes-btn {
  width: 110px;
  padding: 7px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid;
  background: white;
}

.yes-btn {
  color: white; /* White text */
  border-color: #0f766e; /* Teal border */
  background: #0f766e; /* Teal background - filled by default */
}

.yes-btn:hover {
  background: #0d665a; /* Darker teal background */
  border-color: #0d665a; /* Darker teal border */
  color: white; /* White text */
}

.no-btn {
  color: #6b7280; /* Medium gray text - clearly clickable */
  border-color: #d1d5db; /* Light gray border */
  background: white;
}

.no-btn:hover {
  background: #f3f4f6; /* Light gray background */
  border-color: #9ca3af; /* Darker gray border */
  color: #374151; /* Darker gray text */
}

/* Responsive Design */
@media (max-width: 480px) {
  .local-seller-card {
    max-width: 100%;
  }

  .text {
    padding: 12px;
    font-size: 14px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }
}
