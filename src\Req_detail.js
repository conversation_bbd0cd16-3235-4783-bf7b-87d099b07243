import React, { useEffect, useRef, useState } from "react";
import { readCookieREC, getparamValREC, scriptTag, isset, pageViewTrackREC, Eventtracking, currentISO, isPresent } from './common/formCommfun';
import { useGlobalState } from './context/store';
import callPostreq from "./callpostreq";
import SaveisqAPI from "./ISQ/SaveisqAPI";
import Head_scr from "./common/heading";
import callMiniDetAPI from "./MinidtlsAPI";
import { callSaveEnrichAPI } from "./main/callSaveEnrichAPI";

function Req_detail({ form_param, onIsq, isqL, resdata, selectedOptions, cansubmit ,setSelectedOptscr,selectedOptscr}) {
    const [enrichDesc, setEnrichDesc] = useState("");
    const [moreReqShow, setmoreReqShow] = useState(0);
    const { state, dispatch } = useGlobalState();
    const [nec_con_add, setNec_con_add] = useState(false);
    const [warning, setWarning] = useState('');
    const textareaRef = useRef(null);
    var glid = "";

    useEffect(() => {
        const fetchMiniDetAPI = async () => {
            const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
            let mdtlres = null;
            let visitorCity = "";
            try {
                mdtlres = JSON.parse(sessionStorage.getItem("minidtlsres"));
            }
            catch (e) {
                mdtlres = null;
            }
            if (mdtlres && mdtlres[getparamValREC(imesh, "glid")]) {
                let gliddd = getparamValREC(imesh, "glid");
                visitorCity = getparamValREC(imesh, "ctid") ? getparamValREC(imesh, "ctid") : state.UserData.ctid ? state.UserData.ctid : isPresent(mdtlres[gliddd].Response.Data[0]) ? mdtlres[gliddd].Response.Data[0] : "";
                let moreReqShw = isPresent(mdtlres[gliddd].Response.Data[1]) && isPresent(mdtlres[gliddd].Response.Data[2]) && visitorCity != ""  ? 1 : 0;
                setmoreReqShow(moreReqShw);
                dispatch({ type: 'md_resp', payload: { md_resp: mdtlres } });
                
                // setmindet(mdtlres[gliddd]);
            } else {
                if (getparamValREC(imesh, "glid") != "") {
                    const data = await callMiniDetAPI(form_param);
                    visitorCity = getparamValREC(imesh, "ctid") ? getparamValREC(imesh, "ctid") : state.UserData.ctid ? state.UserData.ctid : (data && data.Response && data.Response.Data && isPresent(data.Response.Data[0])) ? data.Response.Data[0] : "";

                    let moreReqShw = data && data.Response && data.Response.Data && isPresent(data.Response.Data[1]) && isPresent(data.Response.Data[2]) && visitorCity != "" ? 1 : 0;
                    setmoreReqShow(moreReqShw);
                    // setmindet(data);
                }
            }


            const visitorFn = getparamValREC(imesh, 'fn') || state.UserData.fn;
            const visitorEm = getparamValREC(imesh, 'em') || state.UserData.em;
            const visitorMb = getparamValREC(imesh, 'mb1') || state.UserData.mb1;
            const visitorIso = state.UserData && state.UserData.iso ? state.UserData.iso : currentISO();

            const nec_con = ((visitorFn == '' || visitorEm == '' || visitorCity == '') && visitorIso == 'IN') || (visitorMb == '' && visitorIso != 'IN') ? true : false;
            setNec_con_add(nec_con);
        };
        fetchMiniDetAPI();

    }, []);


    const handleNextClick = async (e) => {
        e.preventDefault();
        dispatch({ type: 'nextprev', payload: { nextprev: 0 } });
        let value = enrichDesc;
        let isInvalid = scriptTag(value); // Allows empty string
        setWarning(isInvalid ? 'Please do not use special symbols' : '');
        if (!isInvalid && ((isset(() => cansubmit) && cansubmit == 1) || (!isset(() => cansubmit)))) {
            let qid = state.postreq;
            let rdfill = enrichDesc != '' ? '-RDBox-filled' : '-RDBox-empty';
            if (qid == 0) {
                qid = await callPostreq(form_param);
                if (qid != '') {
                    dispatch({ type: 'postreq', payload: { postreq: qid } });
                }
            }
            if(onIsq == 1 && selectedOptscr.length > 0){
                if (isqL == 1) {
                    if (selectedOptscr.length == 1) {
                        if (resdata == 1) {
                            Eventtracking(
                                `SS${window.screencount}-ISQRD-ISQ1-other-filled${rdfill}`,
                                state.prevtrack,
                                form_param,
                                false
                            );
                        }
                    }
                    else{
                        if (resdata == 1) {
                            Eventtracking(
                                `SS${window.screencount}-ISQRD-ISQ1-quantity-filled${rdfill}`,
                                state.prevtrack,
                                form_param,
                                false
                            );
                        }

                    }
                }
                else if (isqL == 2) {
                    if (selectedOptscr.length == 1) {
                        if (resdata == 3) {
                            Eventtracking(
                                `SS${window.screencount}-ISQRD-ISQ2-filled-ISQ3-empty${rdfill}`,
                                state.prevtrack,
                                form_param,
                                false
                            );
                        } else if (resdata == 2) {
                            Eventtracking(
                                `SS${window.screencount}-ISQRD-ISQ2-filled${rdfill}`,
                                state.prevtrack,
                                form_param,
                                false
                            );
                        }
                    }
                    else if (selectedOptscr.length == 2) {
                        Eventtracking(
                            `SS${window.screencount}-ISQRD-ISQ2-filled-ISQ3-filled${rdfill}`,
                            state.prevtrack,
                            form_param,
                            false
                        );
                    }
                } else if (isqL == 3) {
                    if (selectedOptscr.length == 1) {
                        if (resdata == 5) {
                            Eventtracking(
                                `SS${window.screencount}-ISQRD-ISQ4-filled-ISQ5-empty${rdfill}`,
                                state.prevtrack,
                                form_param,
                                false
                            );
                        } else if (resdata == 4) {
                            Eventtracking(
                                `SS${window.screencount}-ISQRD-ISQ4-filled${rdfill}`,
                                state.prevtrack,
                                form_param,
                                false
                            );
                        }
                    }
                    else if (selectedOptscr.length == 2) {
                        Eventtracking(
                            `SS${window.screencount}-ISQRD-ISQ4-filled-ISQ5-filled${rdfill}`,
                            state.prevtrack,
                            form_param,
                            false
                        );
                    }
                }
            }else if (onIsq == 1) {
                if (isqL == 1) {
                    if (selectedOptscr.length == 1) {
                        if (resdata == 1) {
                            Eventtracking(
                                `SS${window.screencount}-ISQRD-ISQ1-other-empty${rdfill}`,
                                state.prevtrack,
                                form_param,
                                false
                            );
                        }
                    }
                    else{
                        if (resdata == 1) {
                            Eventtracking(
                                `SS${window.screencount}-ISQRD-ISQ1-quantity-empty${rdfill}`,
                                state.prevtrack,
                                form_param,
                                false
                            );
                        }

                    }
                }
                else if (isqL == 2) {
                    if (resdata == 3) {
                        Eventtracking(
                            `SS${window.screencount}-ISQRD-ISQ2-empty-ISQ3-empty${rdfill}`,
                            state.prevtrack,
                            form_param,
                            false
                        );
                    } else if (resdata == 2) {
                        Eventtracking(
                            `SS${window.screencount}-ISQRD-ISQ2-empty${rdfill}`,
                            state.prevtrack,
                            form_param,
                            false
                        );
                    }

                } else if (isqL == 3) {
                    if (resdata == 5) {
                        Eventtracking(
                            `SS${window.screencount}-ISQRD-ISQ4-empty-ISQ5-empty${rdfill}`,
                            state.prevtrack,
                            form_param,
                            false
                        );
                    } else if (resdata == 4) {
                        Eventtracking(
                            `SS${window.screencount}-ISQRD-ISQ4-empty${rdfill}`,
                            state.prevtrack,
                            form_param,
                            false
                        );
                    }

                }
                dispatch({ type: 'prevtrack', payload: { prevtrack: `${state.prevtrack}-ISQRD` } });
            }else{
                Eventtracking(
                    `SS${window.screencount}${rdfill}`,
                    state.prevtrack,
                    form_param,
                    false
                );
                dispatch({ type: 'prevtrack', payload: { prevtrack: `${state.prevtrack}-RDBox` } });
            }
            window.screencount++;
            if (onIsq == 1 && selectedOptions.length > 0) {
                const b_response = selectedOptions.map(opt => opt.b_response);
                const q_desc = selectedOptions.map(opt => opt.q_desc);
                const q_id = selectedOptions.map(opt => opt.q_id);
                const b_id = selectedOptions.map(opt => opt.b_id);
                SaveisqAPI(form_param, qid, b_response, q_desc, q_id, b_id);
                setSelectedOptscr([]);
                dispatch({ type: 'prevtrack', payload: { prevtrack: `${state.prevtrack}-ISQRD` } });
            } 
            if (enrichDesc != '') {
                callSaveEnrichAPI(enrichDesc, qid, window.rfq_queryDestinationRec , form_param);
                if(state.localenqparam && state.postreqenqlocal){
                    callSaveEnrichAPI(enrichDesc, state.postreqenqlocal, window.rfqDlocal , state.localenqparam);
                }
            }
            dispatch({ type: 'Isqform', payload: { Isqform: false } });
            dispatch({ type: 'RDform', payload: { RDform: false } });
            if (state.newEnq && nec_con_add) {
                dispatch({ type: 'RDNECcon', payload: { RDNECcon: true } });
            }
            else if (moreReqShow != 1)
                dispatch({ type: 'MrEnrForm', payload: { MrEnrForm: true } });
            else
                dispatch({ type: 'thankyou', payload: { thankyou: true } });
        }
        dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep + .15 } });
    };

    const handleTextAreaChange = (event) => {
        setEnrichDesc(event.target.value);
    };
    useEffect(() => {
        if(onIsq != 1){
            Eventtracking("DS" + window.screencount + "-RDBox", state.prevtrack, form_param, false); 
            dispatch({ type: 'currentscreen', payload: { currentscreen: "RDBox" } });
            if (textareaRef.current) {
                textareaRef.current.focus();
            }
        }
    }, []);

    let sbmmtcls = 'befstgo2 hovsub';
    var clsfrm = "";
    if (form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf') {
        clsfrm = "form-group-img";
        sbmmtcls = "submit-button-img";
    }
    return (
        <>   
        <div className='rdDv'>{onIsq==0 ?  <Head_scr scr={"RD"} hash={form_param} /> : <div></div> }            
            { <div className="betarea" >
                <label className="ber-lbl" id="t0901_textarea">Requirement Details</label>
                <textarea ref={textareaRef} id="t0901_reqBoxTemplates" className={`ber-slbox ber-txtarea rn ${warning ? 'highErr' : ''}`} placeholder="Additional details about your requirement..." onChange={handleTextAreaChange} onClick={()=>{setWarning('')}}></textarea>
                {warning && <div className="errorRD">{warning}</div>}
            </div>}
            <div className={clsfrm}><input value="Next" className={sbmmtcls} id="t0901_submit" type="submit" onClick={handleNextClick}></input></div>
            {/* <PhoneNumber fn={fn} em={em} mb={mb} iso={iso} />*/}   </div>
            
        </>
    );
}

export default Req_detail;
