import React from "react";
import Login_Heading from "./Login_Heading";
import { useGlobalState } from '../context/store';

function Head_scr({ scr, hash, id, scount, isqindex }) {
  const { state, dispatch } = useGlobalState();
  let heading = "", bldcls = "befwt";

  if (scr === "otp") {
    if (hash.formType.toString().toLowerCase() === 'enq') {
      heading = "Confirm your requirement";
    } else {
      heading = "Verify your mobile number to receive quotes";
    }
  } else if (scr === "RD") {
    heading = "Almost Done!";
    bldcls = "";
    if(state.newEnq){
      heading = "Provide Additional Requirements (if any)";
    }

  } else if (scr === "isq") {
    if (scount === 1) {
      const boldHead = getisqHead(hash.ctaName);
      if (boldHead !== '') {
        if (boldHead === 'get more photos') {
          heading = "Add a few details of your requirement to <strong>get more photos</strong> and details from the seller quickly";
          bldcls = "";
        } else {
          heading = `<span className='befwt'>${boldHead}</span> by adding a few details of your requirement`;
          bldcls = "";
        }
      } else {
        heading = "Adding a few details of your requirement can get you quick response from the supplier";
        bldcls = "";
        if(state.newEnq){
          heading = "Add few details to get  quick response from the supplier";
        }
      }
    } else {
      heading = "Adding a few details of your requirement can get you quick response from the supplier";
      bldcls = "";
      if(state.newEnq){
        if(isqindex == 0)
          heading = "Add few details to get  quick response from the supplier";
        if(isqindex == 1)
          heading = "Let us know these specifics to refine your requirement";
        if(isqindex == 2)
          heading = "Share a Few More Details to Assist the Supplier";
        if(isqindex == 3)
          heading = "Add more specifications";
        if(isqindex >= 4)
          heading = "Almost Done !! ";
      }
    }
  } else if (scr === "nec") {
    heading = "Supplier wants to know more about you";
    if (hash.formType === "BL") {
      if (state.frscr === 2 || !state.inlineShownfields) {
          heading = "We want to know more about you!";
      } else if (state.frscr === 1 && !state.inlineShownfields) {
          heading = "We want to know more about you!";
      } else {
          heading = "";
      }
    }
    else if(state.RDNECcon ){
      heading = "Add your details to get response from the Supplier";
    }
  } else if (scr === "morereq") {
    heading = "Please provide a few details to get quick response from the supplier";
    bldcls = "";
    if(hash.formType == "BL" && state.frscr==2){
      heading = "Just one step away to connect with verified sellers";
      bldcls = "befwt";
    }
    if(state.newEnq){
      heading = "Tell Us About Your Business";
    }
  }else if(scr === "isqBL"){
    heading = "Share more details about your requirement";
    // bldcls = "befwt"
  }
  else if(scr === "BlLogin"){
    heading = "Looking to buy something?";
    bldcls = "befwt marBTM";
  }

  return (
    <>
      {
        hash.ctaType !== "Image" && hash.ctaType !== "Video" ? 
          (scr === "login" ? 
            <Login_Heading hash={hash} id={id} /> : 
            <div id="t0901_hdg" className="ber-hdg-r" data-role="" style={{}}>
              <span className={bldcls} dangerouslySetInnerHTML={{ __html: heading }}></span>
            </div>
          ) 
          : null
      }
    </>
  );
}


export default Head_scr

function getisqHead(ctaN){
    switch (ctaN.toLowerCase()) {
        case "get best quote":
          return "Get Best Quote";
        case "get quote":
          return "Get Quote";
        case "get latest price":
          return "Get Latest Price"
        case "contact seller":
          return "Contact Seller"
        case "contact seller_next":
          return "Contact Seller"
        case "contact seller_pre":
          return "Contact Seller"
        case "comphoto":
          return "Contact Seller"
        case "comvideo":
          return "Contact Seller"  
        case "contact supplier":
          return "Contact Supplier"
        case "ask price":
          return "Ask Price"
        case "ask for price":
          return "Ask for price"
        case  "get more photos":
        case  "get more photos_next":
        case  "get more photos_pre":    
            return "get more photos"
        case  "get free download":    
            return "Get Free Download"
        default:
            return '';    


}
}