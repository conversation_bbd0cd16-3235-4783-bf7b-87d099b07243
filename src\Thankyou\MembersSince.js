import React from 'react';

const MembersSince = ({memberSinceDisplay , from}) => {
    let clsnm = "dsf pd_aic mr10";
    if(from == 'LocalSellerCard'){
        clsnm = "dsf pd_aic";
    }
    return (
        <div className={clsnm}>
           <i className="sellericons oef0 memSinc" width="14" height="14"></i> 
        <span className="lh11 fs11 on color1 fsblck">
                    {memberSinceDisplay}
                  </span>
                  </div>
    );
};

export default MembersSince;
