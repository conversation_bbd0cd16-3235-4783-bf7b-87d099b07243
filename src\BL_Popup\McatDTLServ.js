// import React from "react";
// import { useGlobalState } from "../context/store";
export default async function mcatdtl(form_param) {
  // const { state, dispatch } = useGlobalState();  
  const webAddressLocation = location.hostname;
  const ServerName = webAddressLocation.match(/^(dev)/) ? "":(webAddressLocation.match(/^stg/)?"":"");
  const url =  `https://apps.imimg.com/index.php?r=postblenq/McatDtl&modid=${form_param.modId}&mcatid=${Number(form_param.mcatId || "-1")}`;
  try {
      const response = await fetch(url, {
        method: 'GET',
        mode: 'cors', 
        cache: 'no-store'
      });

    if (!response.ok) {
      return '';
    }
    const responseData = await response.json();
    return responseData;
  } catch (error) {
    console.error('Failed to call API:', error);
    imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'saveEnrichment_Failed','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
    return '';
  }
}