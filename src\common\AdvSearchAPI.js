import { srchQuryHandling, readCookieREC, getparamValREC, isset } from './formCommfun';
// import { advSearchwidget } from './formCommfun';

export async function callAdvSearchAPI(form_param,src,pageCityId='',cityminidet='') {
    if(!form_param.mcatName && !form_param.prodDispName && !form_param.prodName) {
        return [];
    }

    let prod = form_param.mcatName || form_param.prodDispName || form_param.prodName;
    prod = prod ? prod.replace(/%26/g, '&') : '';
    const prodname = srchQuryHandling(encodeURIComponent(prod));
    if (cityminidet) {
        cityminidet = srchQuryHandling(encodeURIComponent(cityminidet.replace(/%26/g, '&')));
    }
    // let imeshcookie = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
    // let ctid = getparamValREC(imeshcookie, 'ctid') && getparamValREC(imeshcookie, 'ctid') != '1' && getparamValREC(imeshcookie, 'ctid') != '0' ? getparamValREC(imeshcookie, 'ctid') : '';
    // let pageCityId = ctid ? ctid : form_param.pageCityId ? form_param.pageCityId : '';
    // let pageCity = form_param.pageCity ? form_param.pageCity : '' ;
    // if(pageCityId){
    //     pageCity = '';
    // }

    const webAddressLocation = location.hostname;
    var serverName = webAddressLocation.match(/^dev/)
        ? "//dev-apps.imimg.com/"
        : webAddressLocation.match(/^stg/)
            ? "//apps.imimg.com/"
            : "//apps.imimg.com/";
   var url = serverName + `index.php?r=Newreqform/IMSearchAPI&prodname=${prodname}&pageCityId=${pageCityId}&pageCityIdEV=${cityminidet}`;

    try {
        const response = await fetch(url, {
            method: 'GET',
            mode: 'cors'
        });

        if (!response.ok) {
            throw new Error('Failed to call API');
        }

        const data = await response.json();
        if (data && data.results && data.results.length > 0) {
            let newArray = data.results.filter((items) => {
                if (items && items.fields) {
                    if(src!=='bl' && items.fields.deals_in_loc){
                        return false;
                    }
                    if ((items.fields.ecom_cart_url && items.fields.ecom_item_landing_url && items.fields.isEcomFlag) || items.fields.iildisplayflag == false) {
                        return false;
                    } else if (form_param.pDispId && items.fields.displayid == form_param.pDispId) {
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    return false;
                }
            });
            return src=='bl' ? newArray.length > 5 ? newArray.slice(0, 5) : newArray : newArray.length > 3 ? newArray.slice(0, 3) : newArray;
        }
        return [];
    } catch (error) {
        console.error('There was a problem with the fetch operation:', error);
        if (window.imgtm) {
            imgtm.push({ 'event': 'IMEvent-NI', 'eventCategory': 'Forms-Error', 'eventAction': error, 'eventLabel': 'Advanced_Search', 'eventValue': 0, non_interaction: 0, 'CD_Additional_Data': '' });
        }
        return [];
    }
} 