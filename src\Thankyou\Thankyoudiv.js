import React, { useEffect, useState } from "react";
import { reqFormGATrackREC, ISSET, readCookieREC, getparamValREC, isset, newTYform } from '../common/formCommfun';
import OrderDetails from './OrderDetails';
import VerifiedTY from "./VerifiedTY";
import { useGlobalState } from "../context/store";
function Thankyoudiv({ form_param }) {
    const { state } = useGlobalState();
    const [modifiedData, setModifiedData] = useState({}); 
    const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
    let email = getparamValREC(imesh, 'em') || state.UserData.em;
    let em_ver = getparamValREC(imesh, 'ev') || state.UserData.ev;
    let mob = getparamValREC(imesh, 'mb1') || state.UserData.mb1;
    let uv = getparamValREC(imesh, 'uv') || state.UserData.uv;
    let utype = getparamValREC(imesh, 'utyp') || state.UserData.utyp;
    let sup_glid = ISSET(form_param.rcvGlid) ? `?sup_glid=${btoa(form_param.rcvGlid)}` : "";
    let mess = form_param.formType == 'BL' ? "IndiaMART has received your requirement" : "Your details are sent to ";
    let btnurl = utype == 'P' ? "https://seller.indiamart.com/blgen/postbl#tab=managebl/" : 'https://buyer.indiamart.com/buyertools/managebl';
    let verifyUrl = utype == 'P' ? "https://seller.indiamart.com/companyprofile/manageprofile" : "https://buyer.indiamart.com/userprofile/contactprofile/" ;

    useEffect(() => {
        const mcatid = form_param.mcatId;
        let userFilledData = [];
        try {
            userFilledData = JSON.parse(sessionStorage.getItem("userFilledData")) || {};
        } catch (e) {
            userFilledData = [];
        }

        if (isset(() => userFilledData) && isset(() => userFilledData[mcatid])) {
            const originalData = userFilledData[mcatid];
            const newModifiedData = {}; // Create a new object for modified data

            for (let key in originalData) {
                newModifiedData[key] = originalData[key]; // Copy the original data

                if (newModifiedData["Quantity"] && newModifiedData["Quantity Unit"]) {
                    newModifiedData["Quantity"] = `${newModifiedData["Quantity"]} ${newModifiedData["Quantity Unit"]}`;
                    delete newModifiedData["Quantity Unit"];
                }
            }

            setModifiedData(newModifiedData); // Update state with modified data
            console.log(newModifiedData);
        }
    }, [form_param]); // Ensure this effect runs when form_param changes


    return (

        <>
        {newTYform() ? <div className="idsf befs16 plf6010 cbl_jcc">
            <div className="pflx1">
                <div className='topDv'>
                <div className="bethhdg bemb5 idsf id_aic">
                    <div className="circleR">
                        <svg width="22" height="30" viewBox="0 0 50 50" className="check-circle" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M15 25L25 35L45 15" stroke="#fff" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" className="tick"></path> </svg>
                    </div>
                    {isset(()=>form_param.prodName) && form_param.prodName!='' ?  <span className="thYTxt"><span>Thank You for posting requirement for </span><span className='pdNm'>{form_param.prodName}</span></span> : <span className="thYTxt"><span>Thankyou for posting requirement</span></span>   } 
                    
                </div>

                <VerifiedTY form_param={form_param} />
                
            

            
                {((!em_ver && email) || (!uv && mob)) && <div id="" className="be-grbg txt16 txt33 xMt10" data-role="" style={{ marginLeft: '41px',fontSize: '14px' }}>NOTE: Suppliers are more likely to contact verified buyers. <a href={verifyUrl} target="_blank" className='verLink'>Verify Now. </a> </div>}

                </div>
               

                {Object.keys(modifiedData).length > 0 ? <OrderDetails data={modifiedData} /> : null}


            </div>
            
            <div className="ejcsv bemt5 bemb5 dvBorder">

            { form_param.formType=='Enq' ?  <div className='rightTop'><div id="dtlMsg" data-role="" className="txt16 txt33 xMt10">Your details are sent to:</div> 

                <div id="cmpNm" data-role=""><a href={`https://buyer.indiamart.com/enquiry/messagecentre/${sup_glid}`} target="_blank" style={{ color: '#434343' }} onClick={() => { reqFormGATrackREC("ThankYou_message_companyname",form_param) }}>{form_param.rcvName}</a> </div> </div>
            :  
            <div id="blMsgTY" data-role="" className="txt16 txt33 xMt10 rightTop"> IndiaMART has received your requirement</div>  }

            
                

                { form_param.formType=='Enq' && form_param.supNum ? <div className="tsnBtnR idsf id_aic" id="callNum"><i></i>Call {form_param.supNum}</div> : ''  }

                {/* <div className="tsnBtnR idsf id_aic" id="callNum"><i></i>Call 6787687697989</div> */}

                
                <div id="manage_msg" className="bedblk txt16 thbtmnR befwt">
                    {form_param.formType != 'BL'? <a href={`https://buyer.indiamart.com/enquiry/messagecentre/${sup_glid}`} target="_blank" className="tvwBtnR idsf id_aic" id="t0901msglink" onClick={() => { reqFormGATrackREC("ThankYou_message",form_param) }}><i className="chtBtn"></i> Chat With Seller</a> : <a href={btnurl} target="_blank" className="tvwBtnR idsf id_aic"><i className="chtBtn"></i> Manage Your Requirement</a>}
                </div>
            </div>
        </div> : <div className="idsf befs16 plf35 id_aic ebrtbc cbl_jcc">
            <div className="pflx1">
                <div className="bethhdg befwt bemb5 idsf id_aic">
                    <div className="circle">
                        <svg width="32" height="40" viewBox="0 0 50 50" className="check-circle" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M15 25L25 35L45 15" stroke="#fff" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" className="tick"></path> </svg>
                    </div>
                    <span className="an">Thank You!</span>
                </div>
                <div id="'" className="txt16 txt33 xMt10" >{mess}
                    {form_param.formType != 'BL' && <span className="befwt cNlink"><a href={`https://buyer.indiamart.com/enquiry/messagecentre/${sup_glid}`} target="_blank" onClick={() => { reqFormGATrackREC("ThankYou_message_companyname",form_param) }}>{form_param.rcvName}</a></span>}
                </div>
                {!em_ver && email && <div id="" className="be-grbg" data-role="" >Verify your email as suppliers are more likely to contact verified buyers.</div>}
            </div>
            <div className="idsf eptb10 ejcsv bemt5 bemb5">
                <div id="manage_msg" className="bedblk txt16 thbtmn befwt">
                    {form_param.formType != 'BL'? <a href={`https://buyer.indiamart.com/enquiry/messagecentre/${sup_glid}`} target="_blank" className="tvwBtn idsf id_aic" id="t0901msglink" onClick={() => { reqFormGATrackREC("ThankYou_message",form_param) }}><i className="chtBtn"></i> Chat With Seller</a> : <a href={btnurl} target="_blank" className="tvwBtn idsf id_aic"><i className="chtBtn"></i> Manage Your Requirement</a>}

                </div>
            </div>
        </div> }</>
        
    )
}
export default Thankyoudiv;