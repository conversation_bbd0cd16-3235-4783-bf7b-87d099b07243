# Price Widget Component

A React component that displays the lowest priced product from industry benchmarked pricing data. The widget shows different content based on user authentication status and features the best available price with product image.

## Features

- **Industry Price Benchmark**: Shows the cheapest product from 6 API results with image
- **Essential Product Information**: Displays only product name, image, and price
- **Negotiation Messaging**: Strategic messaging to encourage price negotiation and login
- **Authentication-based Display**:
  - Logged-in users see full product details with negotiation tips
  - Non-logged-in users see compelling messaging about market price benefits
- **Compact Design**: Minimal size (max-width: 320px) optimized for content
- **Product Image**: Small product image (55x55px) with fallback for broken images
- **Strategic Messaging**: Encourages users to use benchmark data for better deals
- **Responsive Design**: Adapts to different screen sizes
- **Loading States**: Smooth loading experience with spinners
- **Error Handling**: Graceful fallback when API fails
- **Conversion Focused**: Messaging designed to increase user identification and login

## Usage

```jsx
import PriceWidget from '../components/PriceWidget/PriceWidget';

<PriceWidget 
  form_param={form_param} 
  onLoginClick={() => {
    // Handle login click - scroll to login form
    if (!state.Imeshform) {
      const loginElement = document.getElementById(`t${id}clslog`);
      if (loginElement) {
        loginElement.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }}
/>
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `form_param` | Object | Yes | Form parameters containing product information |
| `onLoginClick` | Function | No | Callback function when login button is clicked |

## Form Param Structure

The component expects the following properties in `form_param`:

```javascript
{
  prodId: 'string',        // Product ID for API call
  displayId: 'string',     // Alternative product ID
  mcatName: 'string',      // Product category name
  category: 'string',      // Alternative category name
  prodName: 'string',      // Product name for display
  // ... other form parameters
}
```

## API Integration

The widget uses `PriceAPI.js` to fetch pricing data. Currently configured with a dummy API that returns mock data for testing.

### API Response Format

```javascript
{
  success: true,
  data: {
    products: [
      {
        id: "prod_001",
        name: "Industrial Machine Type A",
        price: 45000,
        currency: "INR",
        unit: "piece",
        image: "https://example.com/product-image.jpg",
        supplier: "ABC Industries",
        location: "Mumbai"
      },
      // ... 5 more products
    ],
    marketTrend: "stable", // "stable" | "rising" | "falling"
    lastUpdated: "2024-01-01T00:00:00.000Z",
    confidence: 92,        // Percentage (0-100)
    totalSuppliers: 6      // Number of suppliers providing data
  }
}
```

## Authentication Integration

The component integrates with the existing authentication system:

- Uses `state.Imeshform` from global state to check login status
- Uses `readCookieREC("ImeshVisitor")` for additional authentication verification
- Shows blurred content with login prompt for non-authenticated users

## Styling

The component uses `PriceWidget.css` for styling with the following key classes:

- `.price-widget` - Main container
- `.price-widget-content.blurred` - Blurred content for non-logged-in users
- `.login-overlay` - Overlay with login prompt
- `.price-range` - Min/max price display
- `.price-average` - Average price highlight
- `.market-trend` - Trend indicator styling

## Integration Points

The widget has been integrated into the following form component:

1. **Main Enquiry Form** (`src/main/Enq_form.js`) - Normal enquiry form only

Note: The price widget is intentionally excluded from image enquiry forms to maintain their specific user experience and focus on visual content.

## Testing

Run the tests using:

```bash
npm test PriceWidget.test.js
```

The test suite covers:
- Loading states
- Authentication-based content display
- API error handling
- User interactions
- Market trend display

## Configuration

To switch from dummy API to real API:

1. Update the API endpoint in `PriceAPI.js`
2. Replace the mock data logic with actual API calls
3. Update authentication headers as needed

## Browser Support

The component supports all modern browsers and is compatible with the existing React application setup.

## Performance Considerations

- API calls are made only once per component mount
- Loading states prevent layout shifts
- Error boundaries handle API failures gracefully
- Responsive design reduces unnecessary re-renders
