import React, { useState } from 'react';
import { isset, formatINRPrices, reqFormGATrackREC } from '../../common/formCommfun';
import './LocalSellerCard.css';
import GstShow from '../../Thankyou/GstShow';
import SupplierType from '../../Thankyou/SupplierType';
import MembersSince from '../../Thankyou/MembersSince';
import WidgetRatings from '../../Thankyou/WidgetRatings';
import ResponseRate from '../../Thankyou/ResponseRate';

const LocalSellerCard = ({ form_param, searchAPIdata }) => {
    const [showDeals, setShowDeals] = useState(false);

    if (!searchAPIdata || searchAPIdata.length === 0) {
        return null;
    }

    // Get the first product from the API response
    const firstProduct = searchAPIdata[0];
    const {
        large_image, image, price_f, desktop_title_url, title, catalog_url, companyname, city, itemprice, moq_type, supplier_rating, rating_count, CustTypeWt, tscode, pns_success_ratio, gstVerifiedFlag, memberSinceDisplay, freeSupplierVerifiedFlag, isverifiedexporter
    } = firstProduct.fields;
    let icon = <i></i>;
    let dispType = '';
    if (catalog_url) {
        if (isverifiedexporter == 1) {
            icon = <i className="sellericons oef0 veSlr" width="14" height="14"></i>;
            dispType = 'Verified Exporter';
        } //verified exporter
        else if (CustTypeWt == 149 || CustTypeWt == 179 || tscode) {
            icon = <i className="sellericons oef0 tvfSlr" width="14" height="14"></i>;
            dispType = 'TrustSEAL Verified';
        } //trustseal
        else if ((CustTypeWt <= 699)) {
            icon = <i className="sellericons oef0 vpsSlr" width="14" height="14"></i>
            dispType = 'Verified Plus Supplier';
        } //verified plus
        else if ((CustTypeWt > 699) && (CustTypeWt <= 1899) && freeSupplierVerifiedFlag == 1) {
            icon = <i className="sellericons oef0 vsSlr" width="14" height="14"></i>;
            dispType = 'Verified Supplier';
        } //verified
        else { icon = <i></i> }
    }
    let showgst = (gstVerifiedFlag === '1' || gstVerifiedFlag === '0') ? 1 : 0;
    const img = isset(() => large_image) ?
        (large_image).replace("http://", "https://") :
        (isset(() => image) ? (image).replace("http://", "https://") : "");

    let pdpUrl = desktop_title_url && desktop_title_url.split("?");
    pdpUrl = pdpUrl ? pdpUrl[0] : "";

    const handleYesClick = () => {
        setShowDeals(true);
        // Track the action
        reqFormGATrackREC("LocalSeller_YesDeals", form_param);
    };

    const handleNoClick = () => {
        setShowDeals(false);
        // Track the action
        reqFormGATrackREC("LocalSeller_NoThanks", form_param);
    };

    return (
        <div className="local-seller-card">
            {/* Header Section */}
            <div className="local-seller-header">
                <div className="header-text">Send Enquiry to local seller in</div>
                <div className="location-text">{city.toUpperCase()}</div>
            </div>

            {/* Product Card Section */}
            <div className="product-card-section">
                {/* Product Image */}
                <div className="product-image-container">
                    <img src={img} alt={title} className="product-image" />
                  
                </div>

                {/* Product Details */}
                <div className="product-details-container">
                    {/* Product Title */}
                    <div className="product-title-seller">
                        <a
                            href={`${pdpUrl}?ecom`}
                            target="_blank"
                            onClick={() => reqFormGATrackREC("LocalSeller_ProductName", form_param)}
                        >
                            {title}
                        </a>
                    </div>

                    {/* Two Column Layout */}
                    <div className="product-info-columns">
                        {/* Left Column - Rating, Price, and Seller */}
                        <div className="left-column">
                            {/* Star Rating */}
                            {supplier_rating && rating_count && (
                                <div className="product-rating">
                                    <WidgetRatings rating_count={rating_count} supplier_rating={supplier_rating} from={"LocalSellerCard"}/>
                                </div>
                            )}

                            {/* Price */}
                            <div className="product-price-seller">
                                {itemprice ? (
                                    <>
                                        ₹<strong>{formatINRPrices(itemprice)}</strong>
                                        {moq_type && <span> / {moq_type}</span>}
                                    </>
                                ) : (
                                    price_f && price_f.split(";") && price_f.split(";")[1] && price_f.split(";")[1].trim() && (
                                        <>₹<strong>{price_f.split(";")[1].trim().replace("/", " / ")}</strong></>
                                    )
                                )}
                            </div>

                            {/* Seller Info */}
                            <div className="seller-info">
                                <span className="sold-by-text">Sold By: </span>
                                <a
                                    href={`${catalog_url}?ecom`}
                                    target="_blank"
                                    onClick={() => reqFormGATrackREC("LocalSeller_SellerName", form_param)}
                                    className="seller-name"
                                >
                                    {companyname}
                                </a>
                            </div>
                        </div>

                        {/* Right Column - Trust Indicators and Member Info */}
                        <div className="right-column">
                            {/* Trust Indicators */}
                            {(dispType || showgst) && (
                                <div className="trust-indicators">
                                    {showgst && <GstShow gstVerifiedFlag={gstVerifiedFlag} />}
                                    {dispType && <SupplierType dispType={dispType} icon={icon} />}
                                </div>
                            )}

                            {/* Member Info and Response Rate - Side by Side */}
                            {(memberSinceDisplay || pns_success_ratio) && (
                                <div className="member-response-info">
                                    {memberSinceDisplay && <MembersSince memberSinceDisplay={memberSinceDisplay} from={"LocalSellerCard"}/>}
                                    {pns_success_ratio && <ResponseRate pns_success_ratio={pns_success_ratio} from={"LocalSellerCard"}/>}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Action Buttons */}
            <div className="action-section">
                <div className="action-buttons">
                    <button
                        className="no-thanks-btn"
                        onClick={handleNoClick}
                    >
                        No, Thanks
                    </button>
                    <button
                        className="yes-deals-btn"
                        onClick={handleYesClick}
                    >
                        Yes, Show Deals!
                    </button>
                </div>
            </div>
        </div>
    );
};

export default LocalSellerCard;
