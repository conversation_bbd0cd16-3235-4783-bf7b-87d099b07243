import React, { useState, useEffect } from 'react';
import { reqFormGATrackREC } from '../../common/formCommfun';
import './LocalSellerCard.css';
import SaveisqAPI from '../../ISQ/SaveisqAPI';
import callPostreq from '../../callpostreq';
import { useGlobalState } from '../../context/store';

const LocalSellerCard = ({ form_param, searchAPIdata, setsrchapi, selectedOptions, selectedOptscr }) => {
    const { state, dispatch } = useGlobalState();
    const [showConfirmation, setShowConfirmation] = useState(false);
    const [isUnmounting, setIsUnmounting] = useState(false);

    if (!searchAPIdata || searchAPIdata.length === 0) {
        return null;
    }

    // Get the first product from the API response
    const firstProduct = searchAPIdata[0];
    const {
        desktop_title_url, title, catalog_url, companyname, city, supplier_rating,mcatid,mcatname,displayid,catid,original_title,glusrid
    } = firstProduct.fields;


    const localenqparam = {
        modId: form_param.modId,
        formType : form_param.formType,
        pdpTemplate: form_param.pdpTemplate,
        ctaName: form_param.ctaName,
        ctaType: form_param.ctaType,
        ct: form_param.ct,
        MP: form_param.MP,
        ex: form_param.ex,
        isqf: form_param.isqf,
        cmconv: form_param.cmconv,
        isDist: form_param.isDist,
        section : form_param.section,
        position : form_param.position,
        mcatName : mcatname[0],
        sllrRtng : supplier_rating,
        prodName : original_title,
        afflId : form_param.afflId,
        pDispId : displayid,
        mcatId : mcatid[0],
        catId : catid[0],
        prodDispName : title,
        rcvGlid : glusrid,
        modrefType : form_param.modrefType,
        prodServ : form_param.prodServ,
        fromcard : true,
    }
    let pdpUrl = desktop_title_url && desktop_title_url.split("?");
    pdpUrl = pdpUrl ? pdpUrl[0] : "";

    const handleYesClick = async () => {
        // Execute the API calls
        dispatch({ type: 'localenqparam', payload: { localenqparam: localenqparam } });
        let qid = await callPostreq(localenqparam);
        if (qid !== '') {
            dispatch({ type: 'postreqenqlocal', payload: { postreqenqlocal: qid } });
        }
        if (selectedOptions.length > 0) {
            const b_response = selectedOptions.map(opt => opt.b_response);
            const q_desc = selectedOptions.map(opt => opt.q_desc);
            const q_id = selectedOptions.map(opt => opt.q_id);
            const b_id = selectedOptions.map(opt => opt.b_id);

            SaveisqAPI(localenqparam, qid, b_response, q_desc, q_id, b_id);
        }

        reqFormGATrackREC("LocalSeller_YesDeals", form_param);
        // Show confirmation message
        setShowConfirmation(true);
        // Start unmounting animation and then remove after 3 seconds
        setTimeout(() => {
            setIsUnmounting(true);
            setTimeout(() => {
                setsrchapi(null);
            }, 300); // Small delay for smooth animation
        }, 3000);
    };

    const handleNoClick = () => {
        setIsUnmounting(true);
        setTimeout(() => {
            setsrchapi(null);
        }, 300); // Small delay for smooth animation
        reqFormGATrackREC("LocalSeller_NoThanks", form_param);
    };

    return (
        <div className={`local-seller-card ${isUnmounting ? 'unmounting' : ''}`}>
            {showConfirmation ? (
                /* Confirmation Message */
                <div className="confirmation-message">
                    <div className="tick-mark">✓</div>
                    <div className="confirmation-text">Your requirement has been sent to the seller</div>
                </div>
            ) : (
                <>
                    {/* Header Section */}
                    <div className="text">
                        Send Enquiry to <a href={`${catalog_url}?ecom`} target="_blank" onClick={() => reqFormGATrackREC("LocalSeller_SellerName", form_param)} className="seller-name">{companyname}</a> in <span className="city-name">{city.toUpperCase()}</span> for <a href={`${pdpUrl}?ecom`} target="_blank" onClick={() => reqFormGATrackREC("LocalSeller_ProductName", form_param)}>{title}</a>
                    </div>

                    {/* Action Buttons */}
                    <div className="action-section">
                        <div className="action-buttons">
                            <button
                                className="yes-btn"
                                onClick={handleYesClick}
                            >
                                Yes
                            </button>
                            <button
                                className="no-btn"
                                onClick={handleNoClick}
                            >
                                No
                            </button>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
};

export default LocalSellerCard;
