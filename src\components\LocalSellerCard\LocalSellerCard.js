import React, { useState, useEffect } from 'react';
import { Eventtracking, readCookieREC, reqFormGATrackREC } from '../../common/formCommfun';
import './LocalSellerCard.css';
import SaveisqAPI from '../../ISQ/SaveisqAPI';
import callPostreq from '../../callpostreq';
import { useGlobalState } from '../../context/store';

const LocalSellerCard = ({ form_param, searchAPIdata, setsrchapi, selectedOptions }) => {
    const { state, dispatch } = useGlobalState();
    const [showConfirmation, setShowConfirmation] = useState(false);
    const [isUnmounting, setIsUnmounting] = useState(false);
    var gacookie = readCookieREC('_ga') && readCookieREC('_ga').slice(-1) || '';
    var isEvenDigit = !isNaN(gacookie) && Number(gacookie) % 2 == 0;
    let enableLinks = false;
    // if (isEvenDigit) {
    //     enableLinks = true;
    // }
    if (!searchAPIdata || searchAPIdata.length === 0) {
        return null;
    }

    // Get the first product from the API response
    const firstProduct = searchAPIdata[0];
    const {
        desktop_title_url, title, catalog_url, companyname, city, supplier_rating,mcatid,mcatname,displayid,catid,original_title,glusrid
    } = firstProduct.fields;
    const localenqparam = {
        modId: form_param.modId,
        formType : form_param.formType,
        pdpTemplate: form_param.pdpTemplate,
        ctaName: form_param.ctaName,
        ctaType: form_param.ctaType,
        section : form_param.section,
        position : form_param.position,
        mcatName : mcatname[0],
        sllrRtng : supplier_rating,
        prodName : original_title,
        afflId : form_param.afflId,
        pDispId : displayid,
        mcatId : mcatid[0],
        catId : catid[0],
        prodDispName : title,
        rcvGlid : glusrid,
        modrefType : form_param.modrefType,
        prodServ : form_param.prodServ,
        fromcard : true,
    }
    let pdpUrl = desktop_title_url && desktop_title_url.split("?");
    pdpUrl = pdpUrl ? pdpUrl[0] : "";
    useEffect(() => {
        Eventtracking("DS" + window.screencount + "-LocalSellerCard", state.prevtrack,localenqparam , true);
    }, []);
    const handleYesClick = async () => {
        // Execute the API calls
        dispatch({ type: 'localenqparam', payload: { localenqparam: localenqparam } });
        let qid = await callPostreq(localenqparam);
        if (qid !== '') {
            dispatch({ type: 'postreqenqlocal', payload: { postreqenqlocal: qid } });
        }
        if (selectedOptions.length > 0) {
            const b_response = selectedOptions.map(opt => opt.b_response);
            const q_desc = selectedOptions.map(opt => opt.q_desc);
            const q_id = selectedOptions.map(opt => opt.q_id);
            const b_id = selectedOptions.map(opt => opt.b_id);

            SaveisqAPI(localenqparam, qid, b_response, q_desc, q_id, b_id);
        }

        Eventtracking("SS" + window.screencount + "-LocalSeller_YesClicked", state.prevtrack, localenqparam, false);
        // Show confirmation message
        setShowConfirmation(true);
        // Start unmounting animation and then remove after 3 seconds
        // setTimeout(() => {
        //     setIsUnmounting(true);
        //     setTimeout(() => {
        //         setsrchapi(null);
        //     }, 300); // Small delay for smooth animation
        // }, 3000);
    };

    const handleNoClick = () => {
        // setIsUnmounting(true);
        // setTimeout(() => {
        //     setsrchapi(null);
        // }, 300); // Small delay for smooth animation
        Eventtracking("SS" + window.screencount + "-LocalSeller_NoClicked", state.prevtrack, localenqparam, false);
    };

    return (
        <div className={`local-seller-card ${isUnmounting ? 'unmounting' : ''}`}>
            {showConfirmation ? (
                /* Confirmation Message */
                <div className="confirmation-message">
                    <div className="tick-mark">✓</div>
                    <div className="confirmation-text">Your requirement has been sent to the seller</div>
                </div>
            ) : (
                <>
                    {/* Header Section */}
                    <div className="text">
                        Send Enquiry to {enableLinks ? (
                            <a href={`${catalog_url}?ecom`} target="_blank" onClick={() => Eventtracking("LocalSeller_SellerName", state.prevtrack, localenqparam, false)} className="seller-name">{companyname}</a>
                        ) : (
                            <span className="seller-name-no-link">{companyname}</span>
                        )} in <span className="city-name">{city.toUpperCase()}</span> 
                        {/* for {enableLinks ? (
                            <a href={`${pdpUrl}?ecom`} target="_blank" onClick={() => Eventtracking("LocalSeller_ProductName", state.prevtrack, localenqparam, false)} className="product-name-local">{title}</a>
                        ) : (
                            <span className="product-name-no-link">{title}</span>
                        )} */}
                    </div>

                    {/* Action Radio Buttons */}
                    <div className="action-section">
                        <div className="action-buttons">
                            <label className="radio-option">
                                <input
                                    type="radio"
                                    name="localSellerChoice"
                                    value="yes"
                                    onChange={handleYesClick}
                                    className="radio-input"
                                />
                                <span className="radio-custom yes-radio"></span>
                                <span className="radio-label">Yes</span>
                            </label>
                            <label className="radio-option">
                                <input
                                    type="radio"
                                    name="localSellerChoice"
                                    value="no"
                                    onChange={handleNoClick}
                                    className="radio-input"
                                />
                                <span className="radio-custom no-radio"></span>
                                <span className="radio-label">No</span>
                            </label>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
};

export default LocalSellerCard;
