import React, { useState } from 'react';
import { reqFormGATrackREC } from '../../common/formCommfun';
import './LocalSellerCard.css';

const LocalSellerCard = ({ form_param, searchAPIdata, setsrchapi }) => {

    if (!searchAPIdata || searchAPIdata.length === 0) {
        return null;
    }

    // Get the first product from the API response
    const firstProduct = searchAPIdata[0];
    const {
        large_image, image, price_f, desktop_title_url, title, catalog_url, companyname, city, itemprice, moq_type, supplier_rating, rating_count, CustTypeWt, tscode, pns_success_ratio, gstVerifiedFlag, memberSinceDisplay, freeSupplierVerifiedFlag, isverifiedexporter
    } = firstProduct.fields;



    let pdpUrl = desktop_title_url && desktop_title_url.split("?");
    pdpUrl = pdpUrl ? pdpUrl[0] : "";

    const handleYesClick = () => {
        setsrchapi(null);
        // Track the action
        reqFormGATrackREC("LocalSeller_YesDeals", form_param);
    };

    const handleNoClick = () => {
        setsrchapi(null);
        // Track the action
        reqFormGATrackREC("LocalSeller_NoThanks", form_param);
    };

    return (
        <div className="local-seller-card">
            {/* Header Section */}
            <div className="local-seller-header">
                <div className="header-text">Send Enquiry to {<a
                    href={`${catalog_url}?ecom`}
                    target="_blank"
                    onClick={() => reqFormGATrackREC("LocalSeller_SellerName", form_param)}
                    className="seller-name"
                >
                    {companyname}
                </a>} in <div className="location-text">{city.toUpperCase()}</div> for {<div className="product-title-seller">
                    <a
                        href={`${pdpUrl}?ecom`}
                        target="_blank"
                        onClick={() => reqFormGATrackREC("LocalSeller_ProductName", form_param)}
                    >
                        {title}
                    </a>
                </div>}</div>

            </div>

            {/* Action Buttons */}
            <div className="action-section">
                <div className="action-buttons">
                    <button
                        className="yes-btn"
                        onClick={handleYesClick}
                    >
                        Yes
                    </button>
                    <button
                        className="no-btn"
                        onClick={handleNoClick}
                    >
                        No
                    </button>
                </div>
            </div>
        </div>
    );
};

export default LocalSellerCard;
