import React, { useState } from 'react';
import { isset, formatINRPrices, reqFormGATrackREC } from '../../common/formCommfun';
import './LocalSellerCard.css';

const LocalSellerCard = ({ form_param, searchAPIdata }) => {
    const [showDeals, setShowDeals] = useState(false);

    if (!searchAPIdata || searchAPIdata.length === 0) {
        return null;
    }

    // Get the first product from the API response
    const firstProduct = searchAPIdata[0];
    const {
        large_image,
        image,
        price_f,
        desktop_title_url,
        title,
        catalog_url,
        companyname,
        city,
        itemprice,
        moq_type,
        supplier_rating,
        rating_count
    } = firstProduct.fields;

    const img = isset(() => large_image) ?
        (large_image).replace("http://", "https://") :
        (isset(() => image) ? (image).replace("http://", "https://") : "");

    let pdpUrl = desktop_title_url && desktop_title_url.split("?");
    pdpUrl = pdpUrl ? pdpUrl[0] : "";

    // Format price
    let displayPrice = "";
    if (itemprice) {
        displayPrice = `₹${formatINRPrices(itemprice)}${moq_type ? `/${moq_type}` : ''}`;
    } else if (price_f && price_f.split(";") && price_f.split(";")[1] && price_f.split(";")[1].trim()) {
        displayPrice = `₹${price_f.split(";")[1].trim().replace("/", " / ")}`;
    }

    // Generate star rating display
    const renderStars = () => {
        const rating = supplier_rating || 5.0; // Default to 5.0 if no rating
        const stars = [];
        for (let i = 0; i < 5; i++) {
            stars.push(
                <span key={i} className="star">★</span>
            );
        }
        return (
            <div className="rating-container">
                <div className="stars-container">
                    <div className="stars-background">{stars}</div>
                    <div
                        className="stars-filled"
                        style={{ width: `${(rating / 5) * 100}%` }}
                    >
                        {stars}
                    </div>
                </div>
                <span className="rating-text">
                    {rating} • {companyname || "Green Needs"}
                </span>
            </div>
        );
    };

    const handleYesClick = () => {
        setShowDeals(true);
        // Track the action
        reqFormGATrackREC("LocalSeller_YesDeals", form_param);
    };

    const handleNoClick = () => {
        setShowDeals(false);
        // Track the action
        reqFormGATrackREC("LocalSeller_NoThanks", form_param);
    };

    return (
        <div className="local-seller-card">
            {/* Header Section */}
            <div className="local-seller-header">
                <div className="header-text">Get connected with local sellers in</div>
                <div className="location-text">{city.toUpperCase()}</div>
            </div>

            {/* Product Card Section */}
            <div className="product-card-section">
                {/* Product Image */}
                <div className="product-image-container">
                    <a
                        href={`${pdpUrl}?ecom`}
                        target="_blank"
                        onClick={() => reqFormGATrackREC("LocalSeller_ProductImage", form_param)}
                    >
                        <img
                            src={img}
                            alt={title}
                            className="product-image"
                        />
                    </a>
                </div>

                {/* Product Details */}
                <div className="product-details-container">
                    {/* Product Title */}
                    <div className="product-title">
                        <a
                            href={desktop_title_url}
                            target="_blank"
                            onClick={() => reqFormGATrackREC("LocalSeller_ProductName", form_param)}
                        >
                            {title}
                        </a>
                    </div>

                    {/* Rating */}
                    <div className="product-rating">
                        {renderStars()}
                    </div>

                    {/* Price */}
                    {displayPrice && (
                        <div className="product-price">
                            {displayPrice}
                        </div>
                    )}

                    {/* Seller Info */}
                    <div className="seller-info">
                        <span className="sold-by-text">Sold By: </span>
                        <a
                            href={`${catalog_url}?ecom`}
                            target="_blank"
                            onClick={() => reqFormGATrackREC("LocalSeller_SellerName", form_param)}
                            className="seller-name"
                        >
                            {companyname || "Total Enterprises"}
                        </a>
                    </div>

                    {/* Trust Indicators */}
                    <div className="trust-indicators">
                        <span className="verified-badge">
                            <span className="verified-icon">✓</span>
                            Verified
                        </span>
                        <span className="rating-badge">
                            {supplier_rating || "4.8"} ({rating_count ? `${rating_count}k` : "1.1k"})
                        </span>
                    </div>
                </div>
            </div>

            {/* Action Buttons */}
            <div className="action-section">
                <div className="action-buttons">
                    <button
                        className="no-thanks-btn"
                        onClick={handleNoClick}
                    >
                        No, Thanks
                    </button>
                    <button
                        className="yes-deals-btn"
                        onClick={handleYesClick}
                    >
                        Yes, Show Deals!
                    </button>
                </div>

            </div>
        </div>
    );
};

export default LocalSellerCard;
