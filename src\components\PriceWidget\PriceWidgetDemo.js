import React, { useState } from 'react';
import PriceWidget from './PriceWidget';
import { StateProvider } from '../../context/store';
import { defState } from '../../common/defaultState';
import './PriceWidget.css';

// Mock the common functions for demo
const mockReadCookieREC = (cookieName) => {
  if (cookieName === "ImeshVisitor") {
    return window.demoLoggedIn ? "mock-cookie-value" : null;
  }
  return null;
};

// Override the import for demo purposes
jest.mock('../../common/formCommfun', () => ({
  readCookieREC: mockReadCookieREC
}));

const PriceWidgetDemo = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  
  // Set global demo state
  window.demoLoggedIn = isLoggedIn;

  const mockFormParam = {
    prodId: 'demo-product-123',
    mcatName: 'Industrial Equipment',
    prodName: 'Industrial Machinery',
    price: '₹ 50,000/piece',
    rcvName: 'ABC Industries',
    rcvCity: 'Mumbai'
  };

  const demoState = {
    ...defState,
    Imeshform: isLoggedIn
  };

  const handleLoginToggle = () => {
    setIsLoggedIn(!isLoggedIn);
  };

  const handleLoginClick = () => {
    alert('Login button clicked! In real app, this would scroll to login form.');
  };

  return (
    <div style={{ 
      padding: '20px', 
      maxWidth: '400px', 
      margin: '0 auto',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ textAlign: 'center', marginBottom: '20px' }}>
        Price Widget Demo
      </h1>
      
      <div style={{ 
        marginBottom: '20px', 
        padding: '15px', 
        backgroundColor: '#f8f9fa', 
        borderRadius: '8px',
        textAlign: 'center'
      }}>
        <h3>Demo Controls</h3>
        <p>Current Status: <strong>{isLoggedIn ? 'Logged In' : 'Not Logged In'}</strong></p>
        <button 
          onClick={handleLoginToggle}
          style={{
            padding: '10px 20px',
            backgroundColor: isLoggedIn ? '#dc3545' : '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          {isLoggedIn ? 'Logout' : 'Login'}
        </button>
      </div>

      <div style={{ 
        border: '1px solid #ddd', 
        borderRadius: '8px', 
        padding: '10px',
        backgroundColor: 'white'
      }}>
        <h4 style={{ margin: '0 0 15px 0', textAlign: 'center' }}>
          Price Widget Preview
        </h4>
        
        <StateProvider serviceData={demoState}>
          <PriceWidget 
            form_param={mockFormParam}
            onLoginClick={handleLoginClick}
          />
        </StateProvider>
      </div>

      <div style={{ 
        marginTop: '20px', 
        padding: '15px', 
        backgroundColor: '#e9ecef', 
        borderRadius: '8px',
        fontSize: '14px'
      }}>
        <h4>What you're seeing:</h4>
        <ul style={{ margin: '10px 0', paddingLeft: '20px' }}>
          <li><strong>Logged In:</strong> Lowest priced product with image and details</li>
          <li><strong>Not Logged In:</strong> Blurred content with login prompt</li>
          <li><strong>Product Image:</strong> 60x60px product thumbnail</li>
          <li><strong>Essential Info:</strong> Product name and price only</li>
          <li><strong>Market Summary:</strong> Total products and trends</li>
          <li><strong>View All Button:</strong> Access to all 6 products</li>
        </ul>
      </div>

      <div style={{ 
        marginTop: '20px', 
        padding: '15px', 
        backgroundColor: '#fff3cd', 
        borderRadius: '8px',
        fontSize: '13px'
      }}>
        <h4>Technical Details:</h4>
        <ul style={{ margin: '10px 0', paddingLeft: '20px' }}>
          <li>API returns 6 products, widget shows lowest priced</li>
          <li>Automatic price filtering and product selection</li>
          <li>Image fallback for broken/missing product images</li>
          <li>Responsive design adapts to container width</li>
          <li>Error handling for API failures</li>
          <li>Smooth animations and hover effects</li>
        </ul>
      </div>
    </div>
  );
};

export default PriceWidgetDemo;
