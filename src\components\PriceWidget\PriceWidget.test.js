import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import PriceWidget from './PriceWidget';
import { StateProvider } from '../../context/store';
import { defState } from '../../common/defaultState';

// Mock the common functions
jest.mock('../../common/formCommfun', () => ({
  readCookieREC: jest.fn()
}));

// Mock the API
jest.mock('./PriceAPI', () => ({
  __esModule: true,
  default: jest.fn()
}));

import { readCookieREC } from '../../common/formCommfun';
import fetchPriceData from './PriceAPI';

const mockFormParam = {
  prodId: 'test-product-123',
  mcatName: 'Electronics',
  prodName: 'Test Product',
  price: '₹ 1500/piece'
};

const mockPriceData = {
  success: true,
  data: {
    products: [
      {
        id: "prod_001",
        name: "Industrial Machine Type A",
        price: 45000,
        currency: "INR",
        unit: "piece",
        image: "https://example.com/image1.jpg",
        supplier: "ABC Industries",
        location: "Mumbai"
      },
      {
        id: "prod_002",
        name: "Industrial Machine Type B",
        price: 35000,
        currency: "INR",
        unit: "piece",
        image: "https://example.com/image2.jpg",
        supplier: "XYZ Manufacturing",
        location: "Delhi"
      }
    ],
    marketTrend: "stable",
    lastUpdated: new Date().toISOString(),
    confidence: 92,
    totalSuppliers: 6
  }
};

const renderWithProvider = (component, initialState = defState) => {
  return render(
    <StateProvider serviceData={initialState}>
      {component}
    </StateProvider>
  );
};

describe('PriceWidget', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    fetchPriceData.mockResolvedValue(mockPriceData);
  });

  test('renders loading state initially', () => {
    readCookieREC.mockReturnValue(null);
    
    renderWithProvider(
      <PriceWidget form_param={mockFormParam} />,
      { ...defState, Imeshform: false }
    );

    expect(screen.getByText('Loading price data...')).toBeInTheDocument();
  });

  test('shows negotiation messaging for non-logged-in users', async () => {
    readCookieREC.mockReturnValue(null);

    renderWithProvider(
      <PriceWidget form_param={mockFormParam} />,
      { ...defState, Imeshform: false }
    );

    await waitFor(() => {
      expect(screen.getByText('🔒')).toBeInTheDocument();
      expect(screen.getByText('Know the Market Price')).toBeInTheDocument();
      expect(screen.getByText('Get industry benchmark to negotiate better deals')).toBeInTheDocument();
    });
  });

  test('shows lowest priced product for logged-in users', async () => {
    readCookieREC.mockReturnValue('mock-cookie-value');

    renderWithProvider(
      <PriceWidget form_param={mockFormParam} />,
      { ...defState, Imeshform: true }
    );

    await waitFor(() => {
      expect(screen.getByText('Industrial Machine Type B')).toBeInTheDocument(); // Lowest priced product
      expect(screen.getByText('₹35,000')).toBeInTheDocument(); // Lowest price
      expect(screen.getByText('Use this benchmark price to negotiate better deals with suppliers!')).toBeInTheDocument();
    });
  });

  test('handles API error gracefully', async () => {
    readCookieREC.mockReturnValue('mock-cookie-value');
    fetchPriceData.mockResolvedValue({
      success: false,
      error: 'API Error'
    });
    
    renderWithProvider(
      <PriceWidget form_param={mockFormParam} />,
      { ...defState, Imeshform: true }
    );

    await waitFor(() => {
      expect(screen.getByText('Unable to load price information at the moment.')).toBeInTheDocument();
    });
  });




});
