import React, { memo, useEffect, useState } from "react";
import Login_compt from "../Login/Login_compt";
import ISQDtl from "../ISQDtl";
import Left_sec from "./Left_sec";
import { useGlobalState } from "../context/store";
import Contactdtl from "../ContactDetails/ContactDtl";
import Req_detail from "../Req_detail";
import MoreReqDet from "../MoreReqDet";
import User_ver from "../OTP/User_ver";
import IntGenApi from "./IntGenApi";
import Thankyoumain from "../Thankyou/Thankyoumain";
import { readCookieREC, getparamValREC, isset, stopBgScrollREC, imeqglval, SaveISQonCross,currentISO, Eventtracking,isPresent, LoginNewUiPDP, shouldRenderPriceWidget } from "../common/formCommfun";
import callMiniDetAPI from "../MinidtlsAPI";
import ProgressBar from "../progressbar/ProgressBar";
import ContactSupplierButton from "../ContactSupplierPhone";
import PriceWidget from "../components/PriceWidget/PriceWidget";
import { callAdvSearchAPI } from "../common/AdvSearchAPI";
import LocalSellerCard from "../components/LocalSellerCard/LocalSellerCard";
export function Form_enqMemo({form_param, id, close_fun}) {
  const { state, dispatch } = useGlobalState();
  // const form_param = state.form_param;
  // State variables for ImeshVisitor data
  const [fn, setFn] = useState("");
  const [em, setEm] = useState("");
  const [mb, setMb] = useState("");
  const [iso, setIso] = useState("");
  const [phext, setPhext] = useState("");
  const [mdresct, setMdresct] = useState("");
  const [mdrescity, setMdrescity] = useState("");
  const [showThankyou, setShowThankyou] = useState(false);
  const [shouldShowThankyouOnClose, setShouldShowThankyouOnClose] = useState(false);
  const [mindet, setmindet] = useState({});
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [selectedOptscr, setSelectedOptscr] = useState([]); 
  const [prgsabtest, setPrgsabtest] = useState(false);
  const [srchapi, setsrchapi] = useState([]);
  // Fetch and update ImeshVisitor data on component mount
  window.isBLFormOpen = true;
  stopBgScrollREC();
  window.otpcountRes = 0;
  window.otpcountReser = 0;

  useEffect(() => {
    
    const ismshexist = readCookieREC("ImeshVisitor");
    if (ismshexist == null) {
      dispatch({ type: "Imeshform", payload: { Imeshform: false } });
    } else {
      dispatch({ type: "Imeshform", payload: { Imeshform: true } });
    }
    dispatch({ type: "Isqform", payload: { Isqform: false } });
    dispatch({ type: "RDform", payload: { RDform: false } });
    dispatch({ type: "MrEnrForm", payload: { MrEnrForm: false } });
    dispatch({ type: "thankyou", payload: { thankyou: false } });
  }, [form_param]);

  useEffect(() => {
    let progressstep = 0;
    const fetchMiniDetAPI = async () => {
      const imesh = isset(() => readCookieREC("ImeshVisitor")) ? readCookieREC("ImeshVisitor") : "";
      progressstep = imesh != ""? 1 : 0;
      let mdtlres = null;
      let city = "";
      try{
        mdtlres = JSON.parse(sessionStorage.getItem("minidtlsres"));
      }
      catch(e){
          mdtlres=null;
      }
      if(mdtlres && mdtlres[getparamValREC(imesh, "glid")]){
        city =  isPresent(mdtlres[getparamValREC(imesh, "glid")].Response.Data[0] )? mdtlres[getparamValREC(imesh, "glid")].Response.Data[0] : "" ;
        dispatch({ type: 'md_resp', payload: { md_resp: mdtlres} });
        setmindet(mdtlres[getparamValREC(imesh, "glid")]);
      }else{
        if(getparamValREC(imesh, "glid") != ""){
        const data = await callMiniDetAPI(form_param);
        city = data && data.Response && data.Response.Data && isPresent(data.Response.Data[0]) ? data.Response.Data[0] : "";
        if (isset(() => data) && data && data.Response && data.Response.Data) { 
          setmindet(data);
          dispatch({ type: 'md_resp', payload: { md_resp: data.md_resp } });
        }else{setmindet("No Response From Service");}
       
      }
      }
      city = getparamValREC(imesh, "ctid") ? getparamValREC(imesh, "ctid") : state.UserData.ctid ? state.UserData.ctid : city ;
      setMdresct(city ? "1" : "");
      setMdrescity(city);
      const visitorFn = getparamValREC(imesh, "fn") || state.UserData.fn;
      const visitorEm = getparamValREC(imesh, "em") || state.UserData.em;
      const visitorMb = getparamValREC(imesh, "mb1") || state.UserData.mb1;
      const visitorIso = getparamValREC(imesh, "iso") || state.UserData.iso;
      const phext = getparamValREC(imesh, "phcc") || state.UserData.phcc;

      setFn(visitorFn);
      setEm(visitorEm);
      setMb(visitorMb);
      setIso(visitorIso);
      setPhext(phext);

      const nec_con = 
      ((visitorFn == "" ||  city == "") && visitorIso == "IN") ||
        (visitorMb == "" && visitorIso != "IN") 
          ? true 
          : false; 

      const uv = getparamValREC(imesh, "uv") || state.UserData.uv;
      const otp_con = uv != "V" && visitorIso == "IN" ? true : false;
      const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
      const iploc_iso = getparamValREC(iploc, 'gcniso') !== "" ? getparamValREC(iploc, 'gcniso') : "IN";
      if( form_param.formType == "Enq" ){
        dispatch({ type: "newEnq", payload: { newEnq: true } });
      }
      if(form_param.formType == "Enq"){
        dispatch({ type: "progBar", payload: { progBar: true } });
        if(imesh==''){
          dispatch({ type: "progressCnt", payload: { progressCnt: currentISO() || iploc_iso } });
        }
      }
      if (nec_con) {
        dispatch({ type: "NECcon", payload: { NECcon: true } });
      } else {
        dispatch({ type: "NECcon", payload: { NECcon: false } });

        if (otp_con) {
          dispatch({ type: "OTPcon", payload: { OTPcon: true } });
          progressstep=progressstep+1;
        }
        else{
          progressstep=progressstep+2;
        }
      }
      dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep + progressstep } });
    };
    
    fetchMiniDetAPI();
    setPrgsabtest(LoginNewUiPDP(form_param) && !state.Imeshform ? false : true);
    
  }, [state.Imeshform == false,form_param]);
  useEffect(() => {
    let progressstep = 0;
    const fetchMiniDetAPI = async () => {
      const imesh = isset(() => readCookieREC("ImeshVisitor")) ? readCookieREC("ImeshVisitor") : "";
      progressstep = imesh != ""? 1 : 0;
      let mdtlres = null;
      let city = "";
      try{
        mdtlres = JSON.parse(sessionStorage.getItem("minidtlsres"));
      }
      catch(e){
          mdtlres=null;
      }
      if(mdtlres && mdtlres[getparamValREC(imesh, "glid")]){
        city =  isPresent(mdtlres[getparamValREC(imesh, "glid")].Response.Data[0]) ? mdtlres[getparamValREC(imesh, "glid")].Response.Data[0] : "" ;
        dispatch({ type: 'md_resp', payload: { md_resp: mdtlres} });
        setmindet(mdtlres[getparamValREC(imesh, "glid")]);
      }else{
        if(getparamValREC(imesh, "glid") != ""){
        const data = await callMiniDetAPI(form_param);
        city = data && data.Response && data.Response.Data && isPresent(data.Response.Data[0]) ? data.Response.Data[0] : "";
        if (isset(() => data) && data && data.Response && data.Response.Data) { 
          setmindet(data);
          dispatch({ type: 'md_resp', payload: { md_resp: data.md_resp } });
        }else{setmindet("No Response From Service");}
      }
      }

      city = getparamValREC(imesh, "ctid") ? getparamValREC(imesh, "ctid") : state.UserData.ctid ? state.UserData.ctid : city ;
      setMdresct(city != "" ? "1" : "");
      setMdrescity(city);
      const visitorFn = getparamValREC(imesh, "fn") || state.UserData.fn;
      const visitorEm = getparamValREC(imesh, "em") || state.UserData.em;
      const visitorMb = getparamValREC(imesh, "mb1") || state.UserData.mb1;
      const visitorIso = getparamValREC(imesh, "iso") || state.UserData.iso;
      const phext = getparamValREC(imesh, "phcc") || state.UserData.phcc;

      setFn(visitorFn);
      setEm(visitorEm);
      setMb(visitorMb);
      setIso(visitorIso);
      setPhext(phext);
      
    };
    
    fetchMiniDetAPI();
    
  }, [state.newEnq, state.RDform]);
  useEffect(() => {
    dispatch({ type: "postreq", payload: { postreq: 0 } });
    const imesh = isset(() => readCookieREC("ImeshVisitor"))
      ? readCookieREC("ImeshVisitor")
      : "";
    id == "0901" && imesh != "" ? IntGenApi(form_param) : "";
  }, [form_param]);

useEffect(() => {
    const imesh = isset(() => readCookieREC("ImeshVisitor"))
      ? readCookieREC("ImeshVisitor")
      : "";
    const visitorIso = getparamValREC(imesh, "iso") || state.UserData.iso;
    const uv = getparamValREC(imesh, "uv") || state.UserData.uv;
    const otp_con = uv != "V" && visitorIso == "IN" ? true : false;
    if (otp_con == true) {
      dispatch({ type: "OTPcon", payload: { OTPcon: true } });
    } else {
      dispatch({ type: "OTPcon", payload: { OTPcon: false } });
      dispatch({ type: "Isqform", payload: { Isqform: true } });
    }
  }, [state.Imeshform,form_param]);
  const handleKeyPress = (event) => {
      if (event.keyCode === 27) {
          handleClose('EscapeKeyPressed'); 
      }
  };

 
  useEffect(() => {
      document.addEventListener('keydown', handleKeyPress);
      return () => {
          document.removeEventListener('keydown', handleKeyPress);
      };
  }, [form_param]);

  const handleClose = (source = '') => {
    Eventtracking("CS" + window.screencount + "|" + state.currentscreen + "|" + source, state.prevtrack, form_param, false);
    const imeqarr = imeqglval();
    if(!imeqarr.Enq && form_param.formType != 'BL'){
      window.showskipBut=true;
    }
    else{
      window.showskipBut=false;
  }
    if (!showThankyou && (state.postreq != undefined && state.postreq!=0) && !state.thankyou) {
      SaveISQonCross(form_param, selectedOptions, state.postreq)
      setShowThankyou(true);
    } else {
      close_fun();
    }
    state.progBar && dispatch({ type: 'progressstep', payload: { progressstep: 1} });
    dispatch({ type: 'thankyou', payload: { thankyou: false } });
  };
  useEffect(() => {
    if (window.closeform) {
      
      handleClose('ForSoftwareRedirect');
      window.closeform = false;
    }
  }, [window.closeform]);
  useEffect(() => {
      const srchapicall = async () =>  {
          let imeshcookie = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
          const glid = getparamValREC(imeshcookie, 'glid');
          let cityminidet = '';
          let pageCityId = getparamValREC(imeshcookie, 'ctid') ? getparamValREC(imeshcookie, 'ctid') : '';
          if(!pageCityId){
              try {
                  const storedECV = sessionStorage.getItem(`ecv-${glid}`);
                  if (storedECV) {
                      cityminidet = storedECV;
                  }
              } catch (error) {
                  console.error('Error accessing session storage:', error);
              }
              if (!cityminidet) {
                  const data = await callMiniDetAPI(form_param);
                  cityminidet = data && data.Response && data.Response["ECV"] ? data.Response["ECV"] : "";
              }
          }
          if(cityminidet || pageCityId){
            const results = await callAdvSearchAPI(form_param,'enq',pageCityId,cityminidet);
            setsrchapi(results);
          }
      }
      if(!form_param.isbscsame){
        srchapicall();
      }
  }, [form_param]);
  let contactshow = false;
  let contact_no = form_param.supNum;
  const gleven = Number(getparamValREC(readCookieREC("ImeshVisitor"), "glid")) % 2 === 0;
  if(state.Imeshform && form_param.ctaName.includes("Contact Seller") && form_param.modId == "PRODDTL" && gleven && contact_no){
    const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
    const iploc_iso = getparamValREC(iploc, 'gcniso') !== "" ? getparamValREC(iploc, 'gcniso') : "IN";
    const country_code = iploc_iso;
    const country_code_gl =
      isset(() => country_code) && country_code !== "" && country_code !== "IN"
        ? "+91-"
        : "0";
    contact_no = country_code_gl + contact_no;
    contact_no = contact_no.replace(/^0+/s, "0");
    contact_no = contact_no.replace(/^\s+|\s+$/, "");
    contactshow = contact_no ? true : false;
  }
  return (
    <React.Fragment>
      <div className="ber-frwrap" id={`t${id}_bewrapper`} style={{zIndex:"999"}}>
        <div
          className="blckbg"
          id={`t${id}_blkwrap`}
          onClick={() => handleClose("OutsideClicked")}
        ></div>
        <div className="frmcont">
          {state.thankyou || showThankyou? (
            <Thankyoumain form_param={form_param} close_fun={close_fun} />
          ) : (
            <div
              className={`ber-mcont bezid oEq_r eqPdsec eqarch ${contactshow? "contactinfoshow" :""} ${srchapi? "sellercardshow" :""} lrSplit`}
              id={`t${id}_mcontR`}
            >
              <div className="idsf splitid">
                <Left_sec form_param={form_param} id={id} />
                <div id={`t${id}_leftR`} className="ber-Rsc ber-frmpop btPd aligncls">
                  <div
                    id={`t${id}_cls`}
                    className="ber-cls-rec cp"
                    onClick={() => handleClose("CrossButtonClicked")}
                  >
                    X
                  </div>
                  {(mindet || state.Imeshform == false) && <div id={`t${id}_rightsection`}>
                    {contactshow &&
                    <div className="contact-button-wrapper">
                      <ContactSupplierButton phoneNumber={contact_no} />
                    </div>}
                    {shouldRenderPriceWidget(form_param) && !state.isqScreenReached && <PriceWidget form_param={form_param} />}
                    {!state.Imeshform ? (
                      <Login_compt id={id} form_param={form_param}/>
                    ) : state.NECcon == true ? (
                      <Contactdtl
                        fn={fn}
                        em={em}
                        mb={mb}
                        ctid={mdresct}
                        iso={iso}
                        phext={phext}
                        form_param={form_param}
                        MoreReq={false}
                        mdrescity = {mdrescity}
                      />
                    ) : state.OTPcon == true ? (
                      <User_ver form_param={form_param} />
                    ) : state.Isqform == true ? (
                      <ISQDtl form_param={form_param} selectedOptions={selectedOptions} setSelectedOptions={setSelectedOptions} selectedOptscr={selectedOptscr} setSelectedOptscr={setSelectedOptscr} />
                    ) : state.RDform == true ? (
                      <Req_detail onIsq={0} form_param={form_param} />
                    ) : (
                      state.newEnq == true  && state.RDNECcon == true? (
                        <Contactdtl
                        fn={fn}
                        em={em}
                        mb={mb}
                        ctid={mdresct}
                        iso={iso}
                        phext={phext}
                        form_param={form_param}
                        MoreReq={false}
                        mdrescity = {mdrescity}
                      />
                      ) : ""
                    )}
                    {state.MrEnrForm && <MoreReqDet form_param = {form_param} md_resp={state.md_resp} />}
                    {state.isqScreenReached && srchapi && srchapi.length>0 && <LocalSellerCard form_param={form_param} searchAPIdata={srchapi} setsrchapi={setsrchapi} selectedOptions={selectedOptions} selectedOptscr={selectedOptscr}/>}
                  </div>}
                  {prgsabtest && (!(state.thankyou || showThankyou) && state.progBar && state.progressCnt) && <ProgressBar  progress={state.progressstep}/>}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </React.Fragment>
  );
}
const Enq_form = memo(Form_enqMemo);
export default Enq_form;
